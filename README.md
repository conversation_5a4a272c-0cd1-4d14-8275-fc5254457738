**SUPPORT ENGINEER ROLE TASKS

# Exercise 1

## Task 1.

1. JQ to extract current replica.spec.replicas
2. JQ to extract the deployment strategy.spec.strategy
3. JQ to extract the “service” label of the deployment concatenated with the “environment” label of the deployment, with a hyphen (-) in the middle  .metadata.labels | .service + "-" + .environment

## Task 2.

* JQ to extract all of the issue IDs (for example SAMPLE-123) for all subtasks, in an array  .fields.subtasks | map(.id)

# Exercise 2

Using the port credentials to create a port account, the GitHub app is installed to the port account and the Jira integration is done using the scheduled workflow on GitHub
The Jira issue is related to the Repository blueprint on the data model.

![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXdiJQhAzF0yVmt36OMjIEZsbVRZy9Ym8yN8JdQpeFp90AJV-IrjjDuLrG92lIX4OD23484vYcvnLTc4-72CBonQOJojnmcXQXbjH3wrrwnD5fo2hT6mhdBxtZPkCbtOdZJgG9WQLA?key=KiLXQHrkgBd-SxCs2HeFfw)

![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXfpujcZ3uox5w4k00bqdTEsOauEPi9NcUQcMWLNP3bElh_K9Xq9qp06pxzzroubRssekQOTyUy-s7OKyMtqcHctBsgeej-_f-GVM_l1eqQVUlKFyGWCCSBE3_4xPMGk3yK2y9X7cg?key=KiLXQHrkgBd-SxCs2HeFfw)

Created components in Jira with names mapping to the repository available on the GitHub account,

![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXf63sfVYRgJAKL8wYByTSR0au2r9QIZbyd6YH7JrpmWpi1W2W3ZqjL87jyOH2TiUgvp7TZHAWtRMIKEtCZ4_swXO3bIHN4QSS6gcIGo68bWPohlFSuVTEpaImk84fvLDR49kgQSKQ?key=KiLXQHrkgBd-SxCs2HeFfw)

The mappings on Jira is updated so that the components attached to an issue is related to the corresponding GitHub repository. A Jira issue can have more than one components connected to it and as such in port it maps to more than one repository entity.

![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXeH8zai6pIRHS2840rK2lOOAaqVqXyuDf1zs5PpTnECzKkSpOwDoNq2MPbD7x686szUvn1k7qfo3hEE1uPWnWTvmLmsQqUECE_AC27ESzyMtU9OULJdypqaOJ3eAPKl65ohx1b17Q?key=KiLXQHrkgBd-SxCs2HeFfw)

![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXcdwvbJahZdAcFZDeXUFMHoOgKHD-vTTogy-ExrmxySqTXusNnk9A73HjRvjBfWrcQnSBWi7S_XGb_oQSAisc5jwHJKRRTbtF4ok_GrQTewZldrfRX3TsLZfdqV5m2g1nbUxhAMlw?key=KiLXQHrkgBd-SxCs2HeFfw)

![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXcNiasejEYGUxS62lw1_asqYBwvh9XXJymXwwcAKX6atZPpTZigC0kO4NT7c4MHxkZBypOKTRQYygZ4AyYP2_DVJOZA5aCF2Wws__uljBkS5KohahYLvS36xkw68GGfvuXrBBJE?key=KiLXQHrkgBd-SxCs2HeFfw)

The JQ to connect the components to the repository is

repository: >-

if .fields.components then [ .fields.components[].name ]  else

    null end

# Exercise 3

Adding a property to the repository to count the number of open PRs and adding a scorecard to make use of the property.The property to get the PR count

![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXeiO4hi6ajD3y-E0UYk1kWlhjKq0vcpDHr6X0A1s0r28cu3sb69v4UC72vPynefW4wZkCRfvqre8NdZHW9UAaB4RjC2dxODJGFOKpMIjC3VdWqvL-HTJQArBpX_cu3aCHcJG8IVew?key=KiLXQHrkgBd-SxCs2HeFfw)

![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXc1s_M7vxVajtkrmDiGDPXZcDpUkZsuDIQJbwhNjeewFaLQWKJT_jYdIW7dxQQXHI5XdO2RaShzVpBipVI1-NAPKkaORKfwE1velwBpGW88g3uvWeomFlftX7F4yRg8_ldu6N0b7A?key=KiLXQHrkgBd-SxCs2HeFfw)

![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXdCuQIzbVttZI6tN05y-RO0gm31p7MiwmUDQggT94sDfCYCcb1LnSaOm_-l136AjxiWv9KULIdhoeboTpKPTV9IsjAdgJb5-GnDacmavfXp8DfnlXRhzet_Dz_BzWcKKDBgmTYEjw?key=KiLXQHrkgBd-SxCs2HeFfw)

![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXfP2OzHIQSeZHWrE1b_OCO4zzN6gRc1Vi0ZVPEtkiccor_L6zziisTQl0YojM1QfaKDAK3BkvWnFd5JwQKI8iYqU1sm9suGTy-9Pgnjz_82bKXOXR-JK1IgTxcO0qm6KxqWPvPG9g?key=KiLXQHrkgBd-SxCs2HeFfw)

![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXcpU4xgYlOFTLoZTvjLgHQzd0YUX7g3hHz9v71g-RRXsLVzBma5zJT-8KtTub39UBVsqose9N3dnwSa2Oc8vEJxMoijSCHoIIICB2iDbe2Xwrcb2dZgDcrJCnT47WGp9R_qZ2f6bQ?key=KiLXQHrkgBd-SxCs2HeFfw)

![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXco1K3XPbRsmzbz-0jMnsqbIHWomY0bWqaVIIq4FbhL-2B248dO_O8-r8qULvlyZ2DO9teKOQ258N1ulQe-n7jPoQo-164YQ0cq7rHuJOVjbTSMYXu57295FHoCcgEP54l_1wWbAQ?key=KiLXQHrkgBd-SxCs2HeFfw)

# Exercise 4

A customer having issues with the self service action triggers to deploy a github workflow may encounter issues with the setup especially with the input and these can be the main issue if the data ingested is not well formatted.

Users can take the following steps to troubleshoot and debug

* Verify the GitHub workflow is correctly deployed and running correctly
* Verify the basic details is properly populated
* Verify the form data for user inputs is also well defined
* Ensure the backend invocation and setup are correctly configured
* Validate the permissions and ensure the user has the correct permissions
* Read the error logs generated from the workflow file on the hosted server (GitHub).

**
